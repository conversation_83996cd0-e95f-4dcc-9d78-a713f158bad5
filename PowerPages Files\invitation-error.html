<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">
<link rel="stylesheet" href="invitation-error.css">

<!-- Secure configuration -->
<script>
  // Secure configuration management - get from Power Pages settings
  window.appConfig = {
    functionUrl: {{ settings['AzureFunctionUrl'] | default: 'ERROR_MISSING_AzureFunctionUrl' | json }},
    applicationName: {{ settings['ApplicationName'] | default: 'ERROR_MISSING_ApplicationName' | json }},
    supportEmail: {{ settings['SupportEmail'] | default: '' | json }}
  };

  // Debug logging for configuration
  console.log("Invitation Error Page Configuration:", {
    applicationName: window.appConfig.applicationName,
    supportEmail: window.appConfig.supportEmail
  });
</script>

<div class="invitation-error-container">
  <div class="error-header">
    <i class="fas fa-exclamation-triangle error-icon"></i>
    <h1>Invitation Error</h1>
    <p class="error-subtitle">There was a problem with your invitation</p>
  </div>

  <div class="error-content">
    <!-- Error message will be populated by JavaScript -->
    <div id="errorMessage" class="error-message">
      <h3>Invalid or Expired Invitation</h3>
      <p>The invitation link you used is either invalid, expired, or has already been used.</p>
    </div>

    <!-- Error details (hidden by default) -->
    <div id="errorDetails" class="error-details hidden">
      <h4>Technical Details</h4>
      <div id="errorDetailsContent"></div>
    </div>

    <!-- Action buttons -->
    <div class="error-actions">
      <div class="primary-actions">
        <a href="/" class="btn btn-primary">
          <i class="fas fa-home"></i>
          Return to Home
        </a>
        <button id="requestNewInvitation" class="btn btn-secondary">
          <i class="fas fa-envelope"></i>
          Request New Invitation
        </button>
      </div>
      
      <div class="secondary-actions">
        <button id="showDetails" class="btn btn-tertiary">
          <i class="fas fa-info-circle"></i>
          Show Technical Details
        </button>
        <a href="mailto:{{ settings['SupportEmail'] | default: '<EMAIL>' }}" class="btn btn-tertiary">
          <i class="fas fa-life-ring"></i>
          Contact Support
        </a>
      </div>
    </div>
  </div>

  <!-- Help section -->
  <div class="help-section">
    <h3>Common Issues</h3>
    <div class="help-items">
      <div class="help-item">
        <i class="fas fa-clock"></i>
        <div>
          <strong>Expired Invitation</strong>
          <p>Invitation links expire after a certain period. Request a new invitation from your administrator.</p>
        </div>
      </div>
      <div class="help-item">
        <i class="fas fa-check-circle"></i>
        <div>
          <strong>Already Used</strong>
          <p>This invitation has already been used to create an account. Try logging in instead.</p>
        </div>
      </div>
      <div class="help-item">
        <i class="fas fa-link"></i>
        <div>
          <strong>Invalid Link</strong>
          <p>The invitation link may be corrupted. Copy the full link from your email and try again.</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact information -->
  <div class="contact-section">
    <h4>Need Help?</h4>
    <p>If you continue to experience issues, please contact our support team:</p>
    <div class="contact-info">
      <a href="mailto:{{ settings['SupportEmail'] | default: '<EMAIL>' }}" class="contact-link">
        <i class="fas fa-envelope"></i>
        {{ settings['SupportEmail'] | default: '<EMAIL>' }}
      </a>
    </div>
  </div>
</div>

<!-- Request new invitation modal -->
<div id="requestInvitationModal" class="modal hidden">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Request New Invitation</h3>
      <button id="closeModal" class="close-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <p>To request a new invitation, please contact your administrator or use the contact information below:</p>
      <div class="contact-info">
        <a href="mailto:{{ settings['SupportEmail'] | default: '<EMAIL>' }}?subject=New Invitation Request&body=Hello,%0D%0A%0D%0AI need a new invitation to access the system.%0D%0A%0D%0AThank you." class="btn btn-primary">
          <i class="fas fa-envelope"></i>
          Send Email Request
        </a>
      </div>
    </div>
  </div>
</div>

<script src="invitation-error.js"></script>
