.invitation-error-container {
    max-width: 800px;
    margin: var(--spacing-lg) auto;
    padding: var(--spacing-xl);
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border-top: 4px solid var(--primary-red);
}

.error-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-border);
}

.error-icon {
    font-size: 4rem;
    color: var(--primary-red);
    margin-bottom: var(--spacing-md);
    display: block;
}

.error-header h1 {
    color: var(--primary-red);
    margin-bottom: var(--spacing-sm);
    font-size: 2.5rem;
    font-weight: 300;
}

.error-subtitle {
    color: var(--gray-medium);
    font-size: 1.1rem;
    margin-bottom: 0;
}

.error-content {
    margin-bottom: var(--spacing-xl);
}

.error-message {
    background-color: var(--primary-red-light);
    border-left: 4px solid var(--primary-red);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

.error-message h3 {
    color: var(--primary-red);
    margin-bottom: var(--spacing-sm);
    font-size: 1.25rem;
}

.error-message p {
    color: var(--gray-dark);
    margin-bottom: 0;
    line-height: 1.6;
}

.error-details {
    background-color: var(--gray-light);
    border: 1px solid var(--gray-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.error-details h4 {
    color: var(--gray-dark);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
}

#errorDetailsContent {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    color: var(--gray-medium);
    background-color: var(--color-white);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    border: 1px solid var(--gray-border);
    white-space: pre-wrap;
    word-break: break-word;
}

.error-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.primary-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.secondary-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    flex-wrap: wrap;
}

.error-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 160px;
    justify-content: center;
}

.error-actions .btn i {
    font-size: 0.875rem;
}

.help-section {
    background-color: var(--gray-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.help-section h3 {
    color: var(--color-black);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.help-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.help-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--color-white);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-red);
}

.help-item i {
    color: var(--primary-red);
    font-size: 1.25rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.help-item strong {
    color: var(--color-black);
    display: block;
    margin-bottom: 0.25rem;
}

.help-item p {
    color: var(--gray-dark);
    margin-bottom: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

.contact-section {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: var(--gray-light);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-border);
}

.contact-section h4 {
    color: var(--color-black);
    margin-bottom: var(--spacing-sm);
}

.contact-section p {
    color: var(--gray-dark);
    margin-bottom: var(--spacing-md);
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.contact-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-red);
    text-decoration: none;
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--primary-red);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.contact-link:hover {
    background-color: var(--primary-red);
    color: var(--color-white);
    text-decoration: none;
}

.contact-link i {
    font-size: 0.875rem;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-border);
}

.modal-header h3 {
    color: var(--color-black);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-medium);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--primary-red);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-body p {
    margin-bottom: var(--spacing-md);
}

.modal-body .contact-info {
    justify-content: flex-start;
}

.hidden {
    display: none !important;
}

@media (max-width: 768px) {
    .invitation-error-container {
        margin: var(--spacing-md) auto;
        padding: var(--spacing-md);
        max-width: 95%;
    }

    .error-header h1 {
        font-size: 2rem;
    }

    .error-icon {
        font-size: 3rem;
    }

    .primary-actions,
    .secondary-actions {
        flex-direction: column;
        align-items: center;
    }

    .error-actions .btn {
        min-width: 200px;
    }

    .help-items {
        gap: var(--spacing-sm);
    }

    .help-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .help-item i {
        align-self: center;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .invitation-error-container {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm) auto;
    }

    .error-header h1 {
        font-size: 1.75rem;
    }

    .error-icon {
        font-size: 2.5rem;
    }

    .error-actions .btn {
        min-width: 100%;
        padding: var(--spacing-md);
    }
}
