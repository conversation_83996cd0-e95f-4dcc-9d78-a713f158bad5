<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Invitation - Osler</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="power-pages-styles.css" rel="stylesheet">
    <link href="send-invitation.css" rel="stylesheet">

    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="invitation-function-key" content="{{ settings['Invitation Function Key'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const invitationFunctionKey = document.querySelector('meta[name="invitation-function-key"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;

        return {
          functionUrl: functionUrl || null,
          invitationFunctionKey: invitationFunctionKey || null,
          applicationName: applicationName || null
        };
      }

      window.appConfig = getConfig();
    </script>
</head>
<body>
    <div class="send-invitation-container">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h3 class="text-center mb-0">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send User Invitation
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <!-- Messages -->
                            <div id="errorMessage" class="alert alert-danger d-none" role="alert">
                                <span id="errorText"></span>
                            </div>
                            <div id="successMessage" class="alert alert-success d-none" role="alert">
                                <span id="successText"></span>
                            </div>

                            <!-- Invitation Form -->
                        <form id="invitationForm">
                            <!-- Email Field -->
                            <div class="form-group mb-3">
                                <label for="email" class="form-label fw-bold">Email Address</label>
                                <input type="email" class="form-control" id="email" required maxlength="256"
                                       placeholder="Enter email address">
                                <div class="form-text">The user will receive an invitation email at this address</div>
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- First Name -->
                            <div class="form-group mb-3">
                                <label for="firstName" class="form-label fw-bold">First Name</label>
                                <input type="text" class="form-control" id="firstName" required maxlength="50"
                                       placeholder="Enter first name">
                                <div class="invalid-feedback"></div>
                            </div>

                            <!-- Last Name -->
                            <div class="form-group mb-3">
                                <label for="lastName" class="form-label fw-bold">Last Name</label>
                                <input type="text" class="form-control" id="lastName" required maxlength="50"
                                       placeholder="Enter last name">
                                <div class="invalid-feedback"></div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100" id="sendButton">
                                <i class="fas fa-paper-plane me-2"></i>
                                Send Invitation
                            </button>
                        </form>

                            <!-- Recent Invitations -->
                            <div class="mt-4 pt-3 border-top">
                                <h6 class="text-muted mb-3">Recent Invitations</h6>
                                <div id="recentInvitations" class="small text-muted">
                                    No recent invitations
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="send-invitation.js"></script>
</body>
</html>

